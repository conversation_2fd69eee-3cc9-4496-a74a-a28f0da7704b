#!/usr/bin/env tsx

/**
 * Database Management Script for NextYa
 *
 * IMPORTANT: This project now uses Docker-first database initialization.
 * The schema is defined in /docker/init/01-init.sql and automatically
 * applied when the PostgreSQL container starts.
 *
 * This script is primarily for generating TypeScript types from the
 * existing database schema.
 */

import { Kysely, PostgresDialect } from 'kysely';
import { Pool } from 'pg';
import { exec } from 'child_process';
import { promisify } from 'util';

const execAsync = promisify(exec);

// Database connection
const pool = new Pool({
	host: process.env.DB_HOST || 'localhost',
	user: process.env.DB_USER || 'postgres',
	password: process.env.DB_PASSWORD || 'postgres',
	database: process.env.DB_NAME || 'nextya',
	port: parseInt(process.env.DB_PORT || '5432')
});

async function checkConnection() {
	console.log('🔍 Checking database connection...');
	try {
		const client = await pool.connect();
		await client.query('SELECT 1');
		client.release();
		console.log('✅ Database connection successful');
		return true;
	} catch (error) {
		console.error('❌ Database connection failed:', error);
		return false;
	}
}

async function generateTypes() {
	console.log('🔄 Generating TypeScript types from database schema...');

	try {
		const { stdout, stderr } = await execAsync('npm run db:generate');

		if (stderr && !stderr.includes('warning')) {
			console.error('❌ Error generating types:', stderr);
			return false;
		}

		console.log('✅ TypeScript types generated successfully');
		if (stdout) console.log(stdout);
		return true;
	} catch (error) {
		console.error('❌ Failed to generate types:', error);
		return false;
	}
}

async function showStatus() {
	console.log('\n📊 NextYa Database Status');
	console.log('==========================');
	console.log('🏗️  Schema Source: /docker/init/01-init.sql');
	console.log('🔄 Auto-initialized: When Docker container starts');
	console.log('📝 TypeScript Types: src/lib/database/types.ts');
	console.log('\n💡 To reset database:');
	console.log('   docker-compose down -v && docker-compose up -d');
	console.log('\n💡 To update types after schema changes:');
	console.log('   npm run db:generate');
}

// Parse command line arguments
const command = process.argv[2];

async function main() {
	switch (command) {
		case 'generate':
		case 'types':
			if (await checkConnection()) {
				await generateTypes();
			}
			break;
		case 'status':
		case 'info':
			await showStatus();
			break;
		case 'check':
			await checkConnection();
			break;
		default:
			console.log('NextYa Database Management');
			console.log('=========================');
			console.log('Usage: npm run migrate [command]');
			console.log('');
			console.log('Commands:');
			console.log('  generate, types  Generate TypeScript types from database');
			console.log('  status, info     Show database status and information');
			console.log('  check           Check database connection');
			console.log('');
			console.log('Note: Database schema is auto-initialized via Docker.');
			console.log('See /docker/init/01-init.sql for the schema definition.');
			break;
	}

	await pool.end();
}

main().catch(console.error);
