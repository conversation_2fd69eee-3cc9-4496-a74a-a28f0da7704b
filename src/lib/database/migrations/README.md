# Nextya Database Architecture

## 🎯 Overview

**IMPORTANT**: This directory is now **DEPRECATED**. The database schema has been consolidated into a single source of truth.

## 🏗️ New Architecture

### Single Source of Truth
The database schema is now defined in:
```
/docker/init/01-init.sql
```

This file contains:
- ✅ **All table definitions** with explicit constraint names
- ✅ **All indexes** with consistent naming patterns
- ✅ **All functions** with `fn_` prefix
- ✅ **All triggers** with `tg_` prefix
- ✅ **All views** with `vw_` prefix

### Naming Conventions
- **Primary Keys**: `pk_tablename` (e.g., `pk_users`)
- **Foreign Keys**: `fk_tablename_column` (e.g., `fk_students_user_code`)
- **Unique Constraints**: `uq_tablename_column` (e.g., `uq_users_email`)
- **Check Constraints**: `ck_tablename_column` (e.g., `ck_registers_group_name`)
- **Indexes**: `idx_tablename_column` (e.g., `idx_students_user_code`)
- **Functions**: `fn_descriptive_name` (e.g., `fn_upsert_eval_results`)
- **Triggers**: `tg_tablename_action` (e.g., `tg_users_updated_at`)
- **Views**: `vw_descriptive_name` (e.g., `vw_student_register_results`)

## 🚀 Usage

### Database Initialization
The database is automatically initialized when Docker starts:
```bash
# Start the database with schema
docker-compose up -d

# Generate TypeScript types from schema
npm run db:generate
```

### Development Workflow
```bash
# Reset database (development only)
docker-compose down -v
docker-compose up -d

# Generate types after schema changes
npm run db:generate
```

## 📊 Database Schema

### Core Tables
- **users** - System users (teachers/admins)
- **students** - Student records
- **levels** - Academic levels
- **courses** - Subject courses
- **permissions** - User permissions

### Evaluation Tables
- **evals** - Evaluations/exams
- **eval_sections** - Course-specific sections within evaluations
- **eval_questions** - Individual questions
- **eval_answers** - Student answers
- **eval_results** - Calculated results

### Registration
- **registers** - Student-level assignments

## 🔧 Key Features

### Automatic Calculations
- **Triggers** automatically calculate scores when answers are submitted
- **Total questions** are updated when questions are added/removed
- **Email validation** ensures data integrity

### Performance Optimization
- **Comprehensive indexes** for fast queries
- **Partial indexes** for specific conditions
- **Composite indexes** for complex queries

### Analytics Functions
- `get_student_score_evolution()` - Track student progress over time
- `get_student_course_performance()` - Course-specific performance
- `get_eval_dashboard_data()` - Comprehensive evaluation analytics
- `get_student_eval_report()` - Detailed reporting for CSV export

### Convenient Views
- `v_students_complete` - Students with teacher information
- `v_evaluations_complete` - Evaluations with all related data
- `v_student_results_summary` - Performance summaries
- `v_question_difficulty` - Question analysis
- `v_course_performance` - Course statistics

## 🔄 Migration from Old Structure

### What Changed
- ❌ **Removed**: Duplicate SQL files in `/src/lib/database/sql/`
- ❌ **Removed**: Kysely migration files in `/src/lib/database/migrations/`
- ❌ **Removed**: Legacy Supabase migrations (kept for reference in `/migrations/`)
- ✅ **Added**: Single source of truth in `/docker/init/01-init.sql`

### Benefits
- 🎯 **Single Source**: No more duplication across 3 different locations
- 🏷️ **Consistent Naming**: All constraints explicitly named with patterns
- 🧹 **Clean Structure**: Organized sections for tables, indexes, functions, etc.
- 🔄 **Easy Maintenance**: Changes in one place only

## 🛠️ Development

### Modifying Schema
1. **Edit**: `/docker/init/01-init.sql` (single source of truth)
2. **Reset**: `docker-compose down -v && docker-compose up -d`
3. **Generate**: `npm run db:generate` (update TypeScript types)
4. **Test**: Verify all functionality works

### Adding New Features
1. Add tables/functions to `/docker/init/01-init.sql`
2. Follow naming conventions (pk_, fk_, idx_, fn_, etc.)
3. Add appropriate indexes for performance
4. Update TypeScript types

## 🔍 Troubleshooting

### Common Issues
- **Schema errors**: Check `/docker/init/01-init.sql` syntax
- **Type errors**: Run `npm run db:generate` after schema changes
- **Performance issues**: Review index usage in init file

### Useful Commands
```bash
# Reset database completely
docker-compose down -v
docker-compose up -d

# View database logs
docker logs nextya_postgres

# Generate TypeScript types
npm run db:generate

# Connect to database directly
docker exec -it nextya_postgres psql -U postgres -d nextya
```

## 📝 Key Benefits

- ✅ **Single Source of Truth**: Schema defined in one place only
- ✅ **Explicit Naming**: All constraints have meaningful names
- ✅ **Clean Organization**: Logical sections for different DB objects
- ✅ **Consistent Patterns**: Standardized naming conventions
- ✅ **No Duplication**: Eliminated redundant schema definitions
- ✅ **Easy Maintenance**: Changes in one file only
